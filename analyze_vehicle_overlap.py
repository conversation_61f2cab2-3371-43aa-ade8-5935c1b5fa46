#!/usr/bin/env python3
"""
Script to analyze vehicle overlap between working_vehicles_df and hv_repair_df.
Finds how many vehicles appear in working_vehicles_df but not in hv_repair_df.
"""

import pandas as pd
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_vehicle_overlap():
    """Analyze vehicle overlap between working vehicles and repair data."""
    
    logger.info("Loading data files...")
    
    # Load HV repair data
    hv_repair_df = pd.read_csv("input/hv_repair_2025-06-02b.csv")
    logger.info(f"Loaded {len(hv_repair_df)} HV repair records")
    
    # Load working vehicles data
    working_vehicles_df = pd.read_csv("input/working_matching_vehicles.csv")
    working_unique_df = pd.read_csv("input/working_unique_vehicles.csv")
    
    logger.info(f"Loaded {len(working_vehicles_df)} matching vehicles")
    logger.info(f"Loaded {len(working_unique_df)} unique vehicles")
    
    # Combine working vehicles (same as in preparator.py)
    working_vehicles_df = pd.concat([working_vehicles_df, working_unique_df], ignore_index=True)
    logger.info(f"Total working vehicles: {len(working_vehicles_df)}")
    
    # Clean data - remove null VINs
    hv_repair_df = hv_repair_df.dropna(subset=["vin"])
    working_vehicles_df = working_vehicles_df.dropna(subset=["vin"])
    
    logger.info(f"After cleaning: {len(hv_repair_df)} repair records, {len(working_vehicles_df)} working vehicles")
    
    # Get unique VINs from each dataset
    repair_vins = set(hv_repair_df["vin"].unique())
    working_vins = set(working_vehicles_df["vin"].unique())
    
    logger.info(f"Unique VINs in repair data: {len(repair_vins)}")
    logger.info(f"Unique VINs in working vehicles: {len(working_vins)}")
    
    # Find vehicles that appear in working but not in repair
    working_only_vins = working_vins - repair_vins
    
    # Find vehicles that appear in repair but not in working
    repair_only_vins = repair_vins - working_vins
    
    # Find vehicles that appear in both
    common_vins = working_vins & repair_vins
    
    # Print results
    print("\n" + "="*60)
    print("VEHICLE OVERLAP ANALYSIS")
    print("="*60)
    print(f"Vehicles in working_vehicles_df only: {len(working_only_vins)}")
    print(f"Vehicles in hv_repair_df only: {len(repair_only_vins)}")
    print(f"Vehicles in both datasets: {len(common_vins)}")
    print(f"Total unique vehicles across both: {len(working_vins | repair_vins)}")
    print("="*60)
    
    # Show some examples of working-only vehicles
    if working_only_vins:
        print(f"\nFirst 10 vehicles that appear in working_vehicles_df but NOT in hv_repair_df:")
        for i, vin in enumerate(sorted(working_only_vins)[:10]):
            print(f"  {i+1}. {vin}")
        if len(working_only_vins) > 10:
            print(f"  ... and {len(working_only_vins) - 10} more")
    
    # Show some examples of repair-only vehicles
    if repair_only_vins:
        print(f"\nFirst 10 vehicles that appear in hv_repair_df but NOT in working_vehicles_df:")
        for i, vin in enumerate(sorted(repair_only_vins)[:10]):
            print(f"  {i+1}. {vin}")
        if len(repair_only_vins) > 10:
            print(f"  ... and {len(repair_only_vins) - 10} more")
    
    return {
        'working_only_count': len(working_only_vins),
        'repair_only_count': len(repair_only_vins),
        'common_count': len(common_vins),
        'working_only_vins': working_only_vins,
        'repair_only_vins': repair_only_vins,
        'common_vins': common_vins
    }

if __name__ == "__main__":
    results = analyze_vehicle_overlap()
